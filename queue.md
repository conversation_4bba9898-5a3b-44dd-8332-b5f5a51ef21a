# Webhook Queue System Design
## Cross-Platform Duplicate Prevention for DermaCare

### Executive Summary

This document outlines the design for a robust webhook queue system that prevents duplicate processing across AutoPatient (AP) and CliniCore (CC) platforms. The system uses fire-and-forget HTTP triggers for immediate processing, implements bidirectional duplicate prevention based on causal relationships, and operates within Cloudflare Workers' 30-second runtime constraints.

### Current State Analysis

The existing system processes webhooks directly without queuing:
- Direct webhook processing in `ccWebhookHandler` and `apContactWebhookHandler`
- Sync buffer logic (60-second window) to prevent processing loops
- Cross-platform patient/appointment relationship tracking via database
- Fire-and-forget custom field synchronization using `fireAndForgetRequest` utility
- Previous queue system was removed due to complexity issues

### Requirements

1. **Fire-and-Forget Processing**: Use HTTP triggers instead of cron jobs for immediate webhook processing within 30-second runtime limits
2. **Bidirectional Duplicate Prevention**: Detect duplicates based on causal relationships - if we trigger an API call to one platform, subsequent webhooks from that platform for the same entity are duplicates
3. **Batch Processing**: Process webhooks in batches of maximum 3 requests to optimize performance
4. **Timeout Handling**: Mark any processing exceeding 29 seconds as failed with automatic timeout detection
5. **Retry Logic**: Automatically process retry-eligible entities when no pending entities exist, prioritizing fresh webhooks over retries
6. **Alerting Infrastructure**: Database-backed alerting system with email notifications for timeout scenarios

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐
│   AutoPatient   │    │   CliniCore     │
│    Webhooks     │    │    Webhooks     │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          ▼                      ▼
┌─────────────────────────────────────────┐
│         Webhook Ingestion API           │
│  ┌─────────────────────────────────────┐│
│  │    Bidirectional Duplicate          ││
│  │         Detection                   ││
│  │   ┌─────────────────────────────┐   ││
│  │   │  Causal Relationship        │   ││
│  │   │  Detection (API-triggered   │   ││
│  │   │  vs External webhooks)      │   ││
│  │   └─────────────────────────────┘   ││
│  └─────────────────────────────────────┘│
└─────────────────┬───────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────┐
│            Webhook Queue                │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │Pending  │ │Processing│ │ Retry   │   │
│  │(Fresh)  │ │(Active)  │ │Eligible │   │
│  └─────────┘ └─────────┘ └─────────┘   │
└─────────────────┬───────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────┐
│     Fire-and-Forget HTTP Trigger       │
│  ┌─────────────────────────────────────┐│
│  │   Batch Processing (Max 3)          ││
│  │   29-Second Timeout Detection       ││
│  │   Immediate Processing              ││
│  │   Retry Logic (Fresh > Retries)    ││
│  └─────────────────────────────────────┘│
└─────────────────┬───────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────┐
│       Existing Webhook Processors      │
│  ┌─────────────────┐ ┌─────────────────┐│
│  │  CC Webhook     │ │  AP Webhook     ││
│  │  Processor      │ │  Processor      ││
│  │  (Marks AP      │ │  (Marks CC      ││
│  │   duplicates)   │ │   duplicates)   ││
│  └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────┐
│         Alerting System                 │
│  ┌─────────────────┐ ┌─────────────────┐│
│  │   Database      │ │   Email         ││
│  │   Alerts        │ │   Notifications ││
│  └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────┘
```

## Database Schema

### 1. webhook_queue Table

```sql
CREATE TABLE webhook_queue (
    id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid(),
    source VARCHAR(10) NOT NULL CHECK (source IN ('ap', 'cc')),
    event_type VARCHAR(100) NOT NULL, -- 'patient_created', 'patient_updated', etc.
    entity_type VARCHAR(50) NOT NULL CHECK (entity_type IN ('patient', 'appointment')),
    entity_id VARCHAR(255) NOT NULL, -- Source platform's entity ID
    related_patient_id VARCHAR(255), -- Our internal patient ID for cross-platform linking
    related_appointment_id VARCHAR(255), -- Our internal appointment ID
    payload JSONB NOT NULL, -- Full webhook payload
    status VARCHAR(20) NOT NULL DEFAULT 'pending'
        CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'duplicate_skipped', 'timeout')),
    priority INTEGER NOT NULL DEFAULT 0, -- Higher number = higher priority (fresh = 100, retry = 50)
    retry_count INTEGER NOT NULL DEFAULT 0,
    max_retries INTEGER NOT NULL DEFAULT 3,
    scheduled_at TIMESTAMP NOT NULL DEFAULT NOW(), -- For delayed processing
    started_at TIMESTAMP, -- When processing began
    completed_at TIMESTAMP, -- When processing finished
    timeout_at TIMESTAMP, -- When processing should timeout (started_at + 29 seconds)
    error_message TEXT, -- Last error if failed
    duplicate_of VARCHAR(255), -- Reference to original webhook if duplicate
    duplicate_reason VARCHAR(100), -- Reason for duplicate detection
    api_triggered BOOLEAN NOT NULL DEFAULT FALSE, -- True if this webhook was caused by our API call
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),

    -- Foreign key constraints
    FOREIGN KEY (related_patient_id) REFERENCES patients(id),
    FOREIGN KEY (related_appointment_id) REFERENCES appointments(id),
    FOREIGN KEY (duplicate_of) REFERENCES webhook_queue(id)
);

-- Indexes for performance
CREATE INDEX idx_webhook_queue_status_priority ON webhook_queue(status, priority DESC, created_at ASC);
CREATE INDEX idx_webhook_queue_entity ON webhook_queue(entity_type, entity_id, source);
CREATE INDEX idx_webhook_queue_patient ON webhook_queue(related_patient_id, created_at DESC);
CREATE INDEX idx_webhook_queue_timeout ON webhook_queue(timeout_at) WHERE status = 'processing';
CREATE INDEX idx_webhook_queue_retry ON webhook_queue(retry_count, status) WHERE status = 'failed';
```

### 2. webhook_queue_logs Table

```sql
CREATE TABLE webhook_queue_logs (
    -- Same structure as webhook_queue
    id VARCHAR(255) PRIMARY KEY,
    source VARCHAR(10) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id VARCHAR(255) NOT NULL,
    related_patient_id VARCHAR(255),
    related_appointment_id VARCHAR(255),
    payload JSONB NOT NULL,
    final_status VARCHAR(20) NOT NULL,
    priority INTEGER NOT NULL,
    retry_count INTEGER NOT NULL,
    max_retries INTEGER NOT NULL,
    scheduled_at TIMESTAMP NOT NULL,
    started_at TIMESTAMP,
    completed_at TIMESTAMP NOT NULL,
    timeout_at TIMESTAMP,
    error_message TEXT,
    duplicate_of VARCHAR(255),
    duplicate_reason VARCHAR(100),
    api_triggered BOOLEAN NOT NULL DEFAULT FALSE,
    processing_duration_ms INTEGER, -- Total processing time
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    archived_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Indexes for audit queries
CREATE INDEX idx_webhook_logs_entity ON webhook_queue_logs(entity_type, entity_id, source);
CREATE INDEX idx_webhook_logs_status ON webhook_queue_logs(final_status, archived_at DESC);
CREATE INDEX idx_webhook_logs_timeout ON webhook_queue_logs(final_status, archived_at DESC) WHERE final_status = 'timeout';
```

### 3. duplicate_prevention_locks Table

```sql
CREATE TABLE duplicate_prevention_locks (
    id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_type VARCHAR(50) NOT NULL,
    entity_key VARCHAR(500) NOT NULL, -- Composite key for cross-platform detection
    locked_until TIMESTAMP NOT NULL,
    webhook_id VARCHAR(255) NOT NULL,
    lock_reason VARCHAR(100) NOT NULL, -- 'api_triggered_ap', 'api_triggered_cc', 'processing'
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),

    FOREIGN KEY (webhook_id) REFERENCES webhook_queue(id),
    UNIQUE(entity_type, entity_key)
);

-- TTL index for automatic cleanup
CREATE INDEX idx_duplicate_locks_ttl ON duplicate_prevention_locks(locked_until);
```

### 4. alerts Table

```sql
CREATE TABLE alerts (
    id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid(),
    alert_type VARCHAR(50) NOT NULL, -- 'timeout', 'processing_failure', 'queue_depth', etc.
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    metadata JSONB, -- Additional context data
    webhook_id VARCHAR(255), -- Reference to webhook if applicable
    entity_type VARCHAR(50), -- 'patient', 'appointment', 'system'
    entity_id VARCHAR(255), -- Entity ID if applicable
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'acknowledged', 'resolved')),
    email_sent BOOLEAN NOT NULL DEFAULT FALSE,
    email_sent_at TIMESTAMP,
    resolved_at TIMESTAMP,
    resolved_by VARCHAR(255), -- User or system that resolved the alert
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),

    FOREIGN KEY (webhook_id) REFERENCES webhook_queue(id)
);

-- Indexes for alert management
CREATE INDEX idx_alerts_status ON alerts(status, created_at DESC);
CREATE INDEX idx_alerts_type ON alerts(alert_type, created_at DESC);
CREATE INDEX idx_alerts_severity ON alerts(severity, created_at DESC);
CREATE INDEX idx_alerts_webhook ON alerts(webhook_id) WHERE webhook_id IS NOT NULL;
CREATE INDEX idx_alerts_email ON alerts(email_sent, created_at ASC) WHERE email_sent = FALSE;
```

## Bidirectional Duplicate Detection Strategy

### Causal Relationship Detection

The key insight is that webhooks can be either **externally triggered** (genuine changes) or **API-triggered** (caused by our own API calls). When we make an API call to one platform, subsequent webhooks from that platform for the same entity are duplicates.

```
Scenario 1: CC Webhook Processing
1. CC webhook arrives for patient "123"
2. Processing involves calling AP API to update contact
3. Set duplicate prevention lock: entity="patient:123", reason="api_triggered_ap"
4. Any subsequent AP webhook for same patient within 60 seconds = DUPLICATE

Scenario 2: AP Webhook Processing
1. AP webhook arrives for contact "456"
2. Processing involves calling CC API to update patient
3. Set duplicate prevention lock: entity="patient:456", reason="api_triggered_cc"
4. Any subsequent CC webhook for same patient within 60 seconds = DUPLICATE

Scenario 3: External Change Detection
1. Webhook arrives for entity with no active duplicate prevention lock
2. This is a genuine external change, process normally
3. Set processing lock to prevent race conditions during processing
```

### Duplicate Detection Algorithm

```typescript
interface DuplicateDetectionResult {
    isDuplicate: boolean;
    originalWebhookId?: string;
    reason: string;
    lockReason?: string;
    confidence: 'high' | 'medium' | 'low';
}

async function detectDuplicate(webhook: WebhookQueueItem): Promise<DuplicateDetectionResult> {
    // Step 1: Check for active duplicate prevention locks
    const entityKey = `${webhook.entity_type}:${webhook.related_patient_id || webhook.related_appointment_id}`;
    const activeLock = await findActiveDuplicateLock(entityKey);

    if (activeLock) {
        // Check if this webhook is from the platform we triggered
        const isApiTriggered = (
            (activeLock.lock_reason === 'api_triggered_ap' && webhook.source === 'ap') ||
            (activeLock.lock_reason === 'api_triggered_cc' && webhook.source === 'cc')
        );

        if (isApiTriggered) {
            return {
                isDuplicate: true,
                originalWebhookId: activeLock.webhook_id,
                reason: 'API-triggered duplicate detected',
                lockReason: activeLock.lock_reason,
                confidence: 'high'
            };
        }
    }

    // Step 2: Check for concurrent processing of same entity
    const concurrentWebhooks = await findConcurrentWebhooks(webhook);
    if (concurrentWebhooks.length > 0) {
        return {
            isDuplicate: true,
            originalWebhookId: concurrentWebhooks[0].id,
            reason: 'Concurrent processing detected',
            confidence: 'high'
        };
    }

    // Step 3: Check for recent similar webhooks (timing-based)
    const recentSimilar = await findRecentSimilarWebhooks(webhook, 30); // 30 second window
    if (recentSimilar.length > 0) {
        return {
            isDuplicate: true,
            originalWebhookId: recentSimilar[0].id,
            reason: 'Recent similar webhook detected',
            confidence: 'medium'
        };
    }

    return {
        isDuplicate: false,
        reason: 'No duplicates detected',
        confidence: 'high'
    };
}

async function setDuplicatePreventionLock(
    webhook: WebhookQueueItem,
    targetPlatform: 'ap' | 'cc'
): Promise<void> {
    const entityKey = `${webhook.entity_type}:${webhook.related_patient_id || webhook.related_appointment_id}`;
    const lockReason = `api_triggered_${targetPlatform}`;
    const lockDuration = 60; // 60 seconds

    await createDuplicateLock({
        entity_type: webhook.entity_type,
        entity_key: entityKey,
        locked_until: new Date(Date.now() + lockDuration * 1000),
        webhook_id: webhook.id,
        lock_reason: lockReason
    });
}
```

## Fire-and-Forget Processing Architecture

### Processing Flow

```
┌─────────────────┐
│ Webhook Arrives │
│ (AP or CC)      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 1. Add to Queue │ ← INSERT with priority=100 (fresh)
│   (Immediate)   │   Check for duplicates
│                 │   Set timeout_at = NOW() + 29s
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 2. Fire HTTP    │ ← fireAndForgetRequest('/api/queue/process')
│   Trigger       │   Immediate, non-blocking
│   (Immediate)   │   Uses existing utility
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 3. Batch Query  │ ← Get up to 3 pending webhooks
│   (Max 3)       │   ORDER BY priority DESC, created_at ASC
│                 │   Fresh webhooks (priority=100) first
└─────────┬───────┘
          │
    ┌─────▼─────┐
    │ Any       │
    │ Pending?  │
    └─────┬─────┘
          │
    ┌─────▼─────┐         ┌─────────────────┐
    │    No     │────────▶│ 4a. Check       │
    │           │         │    Retries      │
    └───────────┘         │    (retry_count │
                          │     < max)      │
                          └─────────┬───────┘
    ┌─────▼─────┐                   │
    │    Yes    │             ┌─────▼─────┐
    │           │             │ Process   │
    └─────┬─────┘             │ Retries   │
          │                   │ (Max 3)   │
          ▼                   └───────────┘
┌─────────────────┐
│ 5. Process      │ ← For each webhook in batch:
│   Batch         │   - Set status = 'processing'
│   (Sequential)  │   - Set started_at = NOW()
│                 │   - Set timeout_at = NOW() + 29s
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 6. Duplicate    │ ← Check causal relationships
│   Detection     │   Check active locks
│                 │   Check concurrent processing
└─────────┬───────┘
          │
    ┌─────▼─────┐
    │ Duplicate?│
    └─────┬─────┘
          │
    ┌─────▼─────┐         ┌─────────────────┐
    │    Yes    │────────▶│ 7a. Mark        │
    │           │         │    duplicate_   │
    └───────────┘         │    skipped      │
                          │    Move to Logs │
                          └─────────────────┘
    ┌─────▼─────┐
    │    No     │
    │           │
    └─────┬─────┘
          │
          ▼
┌─────────────────┐
│ 7b. Process     │ ← Call existing processors
│    Webhook      │   Set duplicate prevention locks
│    (Existing    │   Monitor timeout (29s limit)
│    Handlers)    │
└─────────┬───────┘
          │
    ┌─────▼─────┐
    │ Timeout?  │ ← Check if NOW() > timeout_at
    └─────┬─────┘
          │
    ┌─────▼─────┐         ┌─────────────────┐
    │    Yes    │────────▶│ 8a. Mark        │
    │           │         │    timeout      │
    └───────────┘         │    Create Alert │
                          │    Move to Logs │
                          └─────────────────┘
    ┌─────▼─────┐
    │    No     │
    │           │
    └─────┬─────┘
          │
    ┌─────▼─────┐
    │ Success?  │
    └─────┬─────┘
          │
    ┌─────▼─────┐         ┌─────────────────┐
    │    Yes    │────────▶│ 8b. Mark        │
    │           │         │    completed    │
    └───────────┘         │    Move to Logs │
                          └─────────────────┘
    ┌─────▼─────┐
    │    No     │
    │           │
    └─────┬─────┘
          │
          ▼
┌─────────────────┐
│ 8c. Handle      │ ← Check retry_count < max_retries
│    Failure      │   If yes: increment, set priority=50
│                 │   If no: mark failed, move to logs
└─────────────────┘
```

### Fire-and-Forget Processing Implementation

The queue system uses an improved fire-and-forget pattern that automatically extracts the base URL from the current Hono context, eliminating the need to pass context or base URL parameters manually.

```typescript
// Integration with improved fire-and-forget utilities
import { fireAndForgetQueueProcessing, fireAndForgetWebhookIngestion } from '@/queue/fireAndForgetIntegration';

// Webhook ingestion handler - simplified API
export async function addWebhookToQueue(webhook: WebhookPayload): Promise<void> {
    // Add webhook to database queue and trigger processing automatically
    fireAndForgetWebhookIngestion(webhook, {
        trigger: "webhook_added",
        timestamp: new Date().toISOString(),
    });
}

// Manual queue processing trigger - simplified API
export function triggerQueueProcessing(): void {
    fireAndForgetQueueProcessing({}, {
        trigger: "manual",
        timestamp: new Date().toISOString(),
    });
}
```

### Architectural Improvements (v1.1)

The queue system has been enhanced with several architectural improvements to provide a cleaner, more maintainable API:

#### Context-Aware Base URL Extraction

The system now automatically extracts the base URL from the current Hono context using `getContext()`, eliminating the need to pass context or base URL parameters:

```typescript
// Before: Manual context passing
fireAndForgetQueueProcessing(baseUrl, options, context);
extractBaseUrl(c);

// After: Automatic context extraction
fireAndForgetQueueProcessing(options, context);
extractBaseUrl(); // No parameters needed
```

#### Simplified Function Signatures

All fire-and-forget functions have been simplified to remove redundant parameters:

- `fireAndForgetQueueProcessing()` - No longer requires `baseUrl` parameter
- `fireAndForgetWebhookIngestion()` - No longer requires `baseUrl` parameter
- `extractBaseUrl()` - No longer requires `Context` parameter
- `triggerQueueProcessing()` - No longer requires `Context` parameter

#### Improved Type Safety

The system now uses proper TypeScript types instead of `any`:

- Queue processing results use `WebhookProcessingResult` type
- Webhook processors use specific `EventProcessingResult` types
- Integration results use proper union types

#### Backward Compatibility

The "FromContext" convenience functions are maintained for backward compatibility but now serve primarily to add request context information to logs:

```typescript
// Still available for adding request context to logs
fireAndForgetQueueProcessingFromContext(c, options, context);
fireAndForgetWebhookIngestionFromContext(c, webhook, context);
```

// Queue processor endpoint
export async function processQueueBatch(c: Context): Promise<Response> {
    const startTime = Date.now();
    const maxProcessingTime = 29000; // 29 seconds

    try {
        const processor = new WebhookQueueProcessor();

        // Process batch with timeout protection
        const result = await processor.processBatch({
            batchSize: 3,
            timeoutMs: maxProcessingTime,
            startTime
        });

        return c.json(result);
    } catch (error) {
        // Handle timeout or other errors
        await handleProcessingError(error, startTime);
        return c.json({ error: 'Processing failed' }, 500);
    }
}

// Timeout detection and handling
async function handleProcessingError(error: Error, startTime: number): Promise<void> {
    const processingTime = Date.now() - startTime;

    if (processingTime >= 29000) {
        // Create timeout alert
        await createAlert({
            alert_type: 'timeout',
            severity: 'high',
            title: 'Webhook Processing Timeout',
            message: `Processing exceeded 29 seconds (${processingTime}ms)`,
            metadata: { processing_time_ms: processingTime }
        });

        // Mark any processing webhooks as timeout
        await markProcessingWebhooksAsTimeout();
    }
}
```

### API Endpoints

#### 1. Webhook Ingestion

```typescript
// Add webhook to queue (called by existing webhook handlers)
POST /api/queue/webhook
{
    "source": "ap" | "cc",
    "event_type": "patient_created" | "patient_updated" | "appointment_created" | "appointment_updated",
    "entity_type": "patient" | "appointment",
    "entity_id": "string",
    "related_patient_id": "string", // Optional, our internal patient ID
    "related_appointment_id": "string", // Optional, our internal appointment ID
    "payload": object,
    "api_triggered": boolean // Optional, default false
}

Response: {
    "success": boolean,
    "webhook_id": "string",
    "status": "queued" | "duplicate_skipped",
    "duplicate_of": "string", // If duplicate
    "message": "string"
}
```

#### 2. Processing Trigger

```typescript
// Trigger queue processing (fire-and-forget endpoint)
POST /api/queue/process
{
    "trigger_id": "string", // Optional, for correlation
    "batch_size": number // Optional, default 3, max 3
}

Response: {
    "success": boolean,
    "processed_count": number,
    "batch_results": [
        {
            "webhook_id": "string",
            "status": "completed" | "failed" | "duplicate_skipped" | "timeout",
            "processing_time_ms": number,
            "error_message": "string" // If failed
        }
    ],
    "retry_count": number, // Number of retries processed
    "alerts_created": number // Number of alerts generated
}
```

#### 3. Alert Management

```typescript
// Get active alerts
GET /api/alerts
Query params: ?status=active&alert_type=timeout&limit=50

Response: {
    "alerts": [
        {
            "id": "string",
            "alert_type": "timeout" | "processing_failure" | "queue_depth",
            "severity": "low" | "medium" | "high" | "critical",
            "title": "string",
            "message": "string",
            "webhook_id": "string",
            "created_at": "ISO timestamp",
            "status": "active" | "acknowledged" | "resolved"
        }
    ],
    "total_count": number
}

// Acknowledge alert
POST /api/alerts/:id/acknowledge
Response: {
    "success": boolean,
    "message": "string"
}

// Resolve alert
POST /api/alerts/:id/resolve
{
    "resolved_by": "string" // User or system identifier
}
```

## Queue Management Class Design

### WebhookQueueManager Class

```typescript
interface WebhookQueueItem {
    id: string;
    source: 'ap' | 'cc';
    event_type: string;
    entity_type: 'patient' | 'appointment';
    entity_id: string;
    related_patient_id?: string;
    related_appointment_id?: string;
    payload: object;
    status: 'pending' | 'processing' | 'completed' | 'failed' | 'duplicate_skipped' | 'timeout';
    priority: number;
    retry_count: number;
    max_retries: number;
    scheduled_at: Date;
    started_at?: Date;
    completed_at?: Date;
    timeout_at?: Date;
    error_message?: string;
    duplicate_of?: string;
    duplicate_reason?: string;
    api_triggered: boolean;
    created_at: Date;
    updated_at: Date;
}

interface BatchProcessingResult {
    processed_count: number;
    batch_results: WebhookProcessingResult[];
    retry_count: number;
    alerts_created: number;
    total_processing_time_ms: number;
}

interface WebhookProcessingResult {
    webhook_id: string;
    status: 'completed' | 'failed' | 'duplicate_skipped' | 'timeout';
    processing_time_ms: number;
    error_message?: string;
    duplicate_of?: string;
    alerts_created?: string[];
}

class WebhookQueueManager {
    private db: Database;
    private duplicateDetector: DuplicateDetector;
    private alertManager: AlertManager;

    constructor(db: Database) {
        this.db = db;
        this.duplicateDetector = new DuplicateDetector(db);
        this.alertManager = new AlertManager(db);
    }

    /**
     * Add webhook to queue with duplicate detection
     */
    async addWebhook(webhook: Omit<WebhookQueueItem, 'id' | 'created_at' | 'updated_at'>): Promise<{
        webhook_id: string;
        status: 'queued' | 'duplicate_skipped';
        duplicate_of?: string;
    }> {
        // Check for duplicates before adding
        const duplicateResult = await this.duplicateDetector.detectDuplicate(webhook);

        if (duplicateResult.isDuplicate) {
            // Mark as duplicate and move to logs immediately
            const webhookId = crypto.randomUUID();
            await this.moveToLogs({
                ...webhook,
                id: webhookId,
                status: 'duplicate_skipped',
                duplicate_of: duplicateResult.originalWebhookId,
                duplicate_reason: duplicateResult.reason,
                completed_at: new Date(),
                created_at: new Date(),
                updated_at: new Date()
            });

            return {
                webhook_id: webhookId,
                status: 'duplicate_skipped',
                duplicate_of: duplicateResult.originalWebhookId
            };
        }

        // Add to queue with appropriate priority
        const priority = webhook.retry_count > 0 ? 50 : 100; // Fresh = 100, Retry = 50
        const webhookId = await this.insertWebhook({
            ...webhook,
            priority,
            timeout_at: new Date(Date.now() + 29000) // 29 second timeout
        });

        return {
            webhook_id: webhookId,
            status: 'queued'
        };
    }

    /**
     * Process batch of webhooks (max 3)
     */
    async processBatch(options: {
        batchSize?: number;
        timeoutMs?: number;
        startTime?: number;
    } = {}): Promise<BatchProcessingResult> {
        const { batchSize = 3, timeoutMs = 29000, startTime = Date.now() } = options;
        const maxBatchSize = Math.min(batchSize, 3); // Enforce max 3

        // Get pending webhooks (fresh first, then retries)
        const pendingWebhooks = await this.getPendingWebhooks(maxBatchSize);

        if (pendingWebhooks.length === 0) {
            // No pending, check for retry-eligible
            const retryWebhooks = await this.getRetryEligibleWebhooks(maxBatchSize);
            return await this.processBatchItems(retryWebhooks, startTime, timeoutMs);
        }

        return await this.processBatchItems(pendingWebhooks, startTime, timeoutMs);
    }

    /**
     * Process individual batch items
     */
    private async processBatchItems(
        webhooks: WebhookQueueItem[],
        startTime: number,
        timeoutMs: number
    ): Promise<BatchProcessingResult> {
        const results: WebhookProcessingResult[] = [];
        let alertsCreated = 0;
        let retryCount = 0;

        for (const webhook of webhooks) {
            // Check global timeout
            if (Date.now() - startTime >= timeoutMs) {
                await this.handleTimeout(webhook);
                alertsCreated++;
                break;
            }

            const result = await this.processWebhook(webhook);
            results.push(result);

            if (result.alerts_created) {
                alertsCreated += result.alerts_created.length;
            }

            if (webhook.retry_count > 0) {
                retryCount++;
            }
        }

        return {
            processed_count: results.length,
            batch_results: results,
            retry_count: retryCount,
            alerts_created: alertsCreated,
            total_processing_time_ms: Date.now() - startTime
        };
    }

    /**
     * Process individual webhook
     */
    private async processWebhook(webhook: WebhookQueueItem): Promise<WebhookProcessingResult> {
        const processingStart = Date.now();

        try {
            // Mark as processing
            await this.updateStatus(webhook.id, 'processing', {
                started_at: new Date(),
                timeout_at: new Date(Date.now() + 29000)
            });

            // Check for duplicates again (race condition protection)
            const duplicateResult = await this.duplicateDetector.detectDuplicate(webhook);
            if (duplicateResult.isDuplicate) {
                await this.markDuplicateSkipped(webhook.id, duplicateResult);
                return {
                    webhook_id: webhook.id,
                    status: 'duplicate_skipped',
                    processing_time_ms: Date.now() - processingStart,
                    duplicate_of: duplicateResult.originalWebhookId
                };
            }

            // Process with existing handlers
            const processingResult = await this.callExistingProcessor(webhook);

            // Check for timeout
            if (Date.now() >= webhook.timeout_at!.getTime()) {
                await this.handleTimeout(webhook);
                return {
                    webhook_id: webhook.id,
                    status: 'timeout',
                    processing_time_ms: Date.now() - processingStart,
                    alerts_created: ['timeout_alert']
                };
            }

            if (processingResult.success) {
                // Set duplicate prevention locks if API calls were made
                if (processingResult.apiCallsMade) {
                    await this.setDuplicatePreventionLocks(webhook, processingResult.apiCallsMade);
                }

                await this.markCompleted(webhook.id);
                return {
                    webhook_id: webhook.id,
                    status: 'completed',
                    processing_time_ms: Date.now() - processingStart
                };
            } else {
                return await this.handleFailure(webhook, processingResult.error, processingStart);
            }

        } catch (error) {
            return await this.handleFailure(webhook, error as Error, processingStart);
        }
    }

    /**
     * Handle webhook processing failure
     */
    private async handleFailure(
        webhook: WebhookQueueItem,
        error: Error,
        processingStart: number
    ): Promise<WebhookProcessingResult> {
        const processingTime = Date.now() - processingStart;

        if (webhook.retry_count < webhook.max_retries) {
            // Increment retry count and reset to pending
            await this.updateForRetry(webhook.id, error.message);
            return {
                webhook_id: webhook.id,
                status: 'failed',
                processing_time_ms: processingTime,
                error_message: `Retry ${webhook.retry_count + 1}/${webhook.max_retries}: ${error.message}`
            };
        } else {
            // Max retries reached, mark as failed
            await this.markFailed(webhook.id, error.message);
            return {
                webhook_id: webhook.id,
                status: 'failed',
                processing_time_ms: processingTime,
                error_message: `Max retries reached: ${error.message}`
            };
        }
    }

    /**
     * Update webhook status
     */
    async updateStatus(
        webhookId: string,
        status: WebhookQueueItem['status'],
        updates: Partial<WebhookQueueItem> = {}
    ): Promise<void> {
        await this.db.update(webhookQueue)
            .set({
                status,
                updated_at: new Date(),
                ...updates
            })
            .where(eq(webhookQueue.id, webhookId));
    }

    /**
     * Mark webhook as completed and move to logs
     */
    async markCompleted(webhookId: string): Promise<void> {
        const webhook = await this.getWebhookById(webhookId);
        if (!webhook) return;

        await this.moveToLogs({
            ...webhook,
            status: 'completed',
            completed_at: new Date()
        });

        await this.deleteFromQueue(webhookId);
    }

    /**
     * Mark webhook as failed and move to logs
     */
    async markFailed(webhookId: string, errorMessage: string): Promise<void> {
        const webhook = await this.getWebhookById(webhookId);
        if (!webhook) return;

        await this.moveToLogs({
            ...webhook,
            status: 'failed',
            error_message: errorMessage,
            completed_at: new Date()
        });

        await this.deleteFromQueue(webhookId);
    }

    /**
     * Handle timeout scenario
     */
    async handleTimeout(webhook: WebhookQueueItem): Promise<void> {
        // Create timeout alert
        await this.alertManager.createAlert({
            alert_type: 'timeout',
            severity: 'high',
            title: 'Webhook Processing Timeout',
            message: `Webhook ${webhook.id} exceeded 29 second timeout`,
            webhook_id: webhook.id,
            entity_type: webhook.entity_type,
            entity_id: webhook.entity_id,
            metadata: {
                source: webhook.source,
                event_type: webhook.event_type,
                retry_count: webhook.retry_count
            }
        });

        // Mark as timeout and move to logs
        await this.moveToLogs({
            ...webhook,
            status: 'timeout',
            completed_at: new Date()
        });

        await this.deleteFromQueue(webhook.id);
    }

    /**
     * Move webhook to logs table
     */
    private async moveToLogs(webhook: WebhookQueueItem): Promise<void> {
        const processingDuration = webhook.started_at && webhook.completed_at
            ? webhook.completed_at.getTime() - webhook.started_at.getTime()
            : null;

        await this.db.insert(webhookQueueLogs).values({
            ...webhook,
            final_status: webhook.status,
            processing_duration_ms: processingDuration,
            archived_at: new Date()
        });
    }

    /**
     * Get pending webhooks with priority ordering
     */
    private async getPendingWebhooks(limit: number): Promise<WebhookQueueItem[]> {
        return await this.db.select()
            .from(webhookQueue)
            .where(eq(webhookQueue.status, 'pending'))
            .orderBy(desc(webhookQueue.priority), asc(webhookQueue.created_at))
            .limit(limit);
    }

    /**
     * Get retry-eligible webhooks
     */
    private async getRetryEligibleWebhooks(limit: number): Promise<WebhookQueueItem[]> {
        return await this.db.select()
            .from(webhookQueue)
            .where(
                and(
                    eq(webhookQueue.status, 'failed'),
                    lt(webhookQueue.retry_count, webhookQueue.max_retries)
                )
            )
            .orderBy(asc(webhookQueue.created_at))
            .limit(limit);
    }

    /**
     * Handle new webhook arrival during retry processing
     */
    async handleNewWebhookDuringRetry(
        newWebhook: WebhookQueueItem,
        retryWebhook: WebhookQueueItem
    ): Promise<void> {
        // Move retry webhook to logs (superseded by fresh data)
        await this.moveToLogs({
            ...retryWebhook,
            status: 'failed',
            error_message: 'Superseded by fresh webhook',
            completed_at: new Date()
        });

        await this.deleteFromQueue(retryWebhook.id);

        // Process new webhook normally
        await this.addWebhook(newWebhook);
    }
}
```

## Error Handling Strategy

### Error Classification

```typescript
enum ErrorType {
    TRANSIENT = 'transient',     // Network timeouts, API rate limits
    PERMANENT = 'permanent',     // Invalid payload, auth failures
    DUPLICATE = 'duplicate',     // Duplicate detection errors
    PROCESSING = 'processing'    // Business logic errors
}

interface ErrorHandlingConfig {
    transient: {
        maxRetries: 3,
        backoffStrategy: 'exponential', // 30s, 2m, 10m
        retryableStatusCodes: [408, 429, 500, 502, 503, 504]
    },
    permanent: {
        maxRetries: 0,
        alerting: true
    },
    duplicate: {
        maxRetries: 1,
        fallbackToProcessing: true // Process as non-duplicate if detection fails
    },
    processing: {
        maxRetries: 2,
        backoffStrategy: 'linear' // 1m, 2m
    }
}
```

### Retry Logic

```typescript
async function calculateNextRetry(webhook: WebhookQueueItem, error: ProcessingError): Promise<Date> {
    const baseDelays = {
        transient: [30, 120, 600], // 30s, 2m, 10m
        processing: [60, 120],     // 1m, 2m
        duplicate: [30]            // 30s
    };

    const delays = baseDelays[error.type] || baseDelays.transient;
    const retryIndex = Math.min(webhook.retry_count, delays.length - 1);
    const delaySeconds = delays[retryIndex];

    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 0.1 * delaySeconds;
    const totalDelay = delaySeconds + jitter;

    return new Date(Date.now() + totalDelay * 1000);
}
```

## Monitoring and Observability

### Key Metrics

```typescript
interface QueueMetrics {
    // Queue Health
    queue_depth: number;                    // Current pending webhooks
    processing_rate: number;                // Webhooks processed per minute
    average_processing_time_ms: number;     // Average time to process
    oldest_pending_age_minutes: number;     // Age of oldest pending webhook

    // Success Rates
    success_rate_1h: number;               // Success rate last hour
    success_rate_24h: number;              // Success rate last 24 hours
    duplicate_detection_rate: number;      // Percentage of duplicates detected

    // Error Rates
    failure_rate_by_type: {
        transient: number;
        permanent: number;
        processing: number;
        duplicate: number;
    };

    // Retry Statistics
    average_retries_per_webhook: number;
    retry_success_rate: number;

    // Platform Distribution
    webhook_distribution: {
        ap: number;
        cc: number;
    };

    // Entity Distribution
    entity_distribution: {
        patient: number;
        appointment: number;
    };
}
```

### Alerting Rules

```yaml
alerts:
  - name: "High Queue Depth"
    condition: "queue_depth > 100"
    severity: "warning"
    description: "Webhook queue has more than 100 pending items"

  - name: "Processing Stalled"
    condition: "no webhooks processed in 5 minutes AND queue_depth > 0"
    severity: "critical"
    description: "Queue processing appears to be stalled"

  - name: "High Failure Rate"
    condition: "failure_rate_1h > 10%"
    severity: "warning"
    description: "Webhook processing failure rate exceeds 10%"

  - name: "Duplicate Detection Issues"
    condition: "duplicate_detection_rate > 50% OR duplicate_detection_rate < 1%"
    severity: "warning"
    description: "Duplicate detection rate is outside normal range"

  - name: "Old Pending Webhooks"
    condition: "oldest_pending_age_minutes > 30"
    severity: "warning"
    description: "Webhooks have been pending for more than 30 minutes"
```

### Logging Strategy

```typescript
interface WebhookProcessingLog {
    webhook_id: string;
    correlation_id: string;
    timestamp: string;
    level: 'debug' | 'info' | 'warn' | 'error';
    stage: 'queued' | 'processing' | 'duplicate_check' | 'completed' | 'failed';
    message: string;
    metadata: {
        source: 'ap' | 'cc';
        entity_type: 'patient' | 'appointment';
        entity_id: string;
        processing_time_ms?: number;
        retry_count?: number;
        error_type?: string;
        duplicate_of?: string;
    };
}

// Example log entries
{
    "webhook_id": "uuid-123",
    "correlation_id": "req-456",
    "timestamp": "2024-08-06T10:30:00.000Z",
    "level": "info",
    "stage": "duplicate_check",
    "message": "Duplicate detected for patient webhook",
    "metadata": {
        "source": "cc",
        "entity_type": "patient",
        "entity_id": "789",
        "duplicate_of": "uuid-456",
        "detection_reason": "Same entity modified across platforms",
        "confidence": "high"
    }
}
```

## Implementation Plan

### Phase 1: Core Queue Infrastructure (Week 1)

**Deliverables:**
- Database schema creation and migrations
- WebhookQueueManager class implementation
- Fire-and-forget HTTP trigger integration
- Basic API endpoints for webhook ingestion and processing
- Unit tests for core functionality

**Tasks:**
1. Create database migrations for `webhook_queue`, `webhook_queue_logs`, `duplicate_prevention_locks`, and `alerts` tables
2. Implement `WebhookQueueManager` class with all core methods
3. Integrate with existing `fireAndForgetRequest` utility
4. Build API endpoints: `POST /api/queue/webhook`, `POST /api/queue/process`
5. Implement timeout detection and handling (29-second limit)
6. Write unit tests for queue operations and timeout scenarios

**Success Criteria:**
- Webhooks can be added to queue and processed in batches of 3
- Fire-and-forget processing triggers work correctly
- Timeout detection marks webhooks as failed after 29 seconds
- All tests pass with 30-second runtime constraint
- No performance degradation from existing system

### Phase 2: Bidirectional Duplicate Detection (Week 2)

**Deliverables:**
- Causal relationship duplicate detection
- API-triggered webhook prevention
- Duplicate prevention lock management
- Integration tests for bidirectional scenarios
- Performance optimization for detection queries

**Tasks:**
1. Implement `DuplicateDetector` class with causal relationship logic
2. Add API-triggered duplicate prevention locks
3. Create bidirectional detection for AP ↔ CC scenarios
4. Build comprehensive integration tests for all duplicate scenarios
5. Optimize database queries with proper indexing
6. Add duplicate detection metrics and logging

**Success Criteria:**
- API-triggered duplicates detected with 100% accuracy
- Bidirectional duplicate prevention works for both AP and CC
- Detection latency < 50ms for 99% of requests
- No false positives in integration tests
- Comprehensive logging for all duplicate decisions

### Phase 3: Alerting and Retry Logic (Week 3)

**Deliverables:**
- Database-backed alerting system
- Email notification infrastructure
- Retry logic with fresh webhook prioritization
- Error handling and classification
- Monitoring and metrics collection

**Tasks:**
1. Implement `AlertManager` class with database storage
2. Add email notification system for timeout alerts
3. Build retry logic that prioritizes fresh webhooks over retries
4. Add error classification and handling for different error types
5. Implement comprehensive metrics collection
6. Create alerting rules and notification templates

**Success Criteria:**
- Timeout alerts generated and emails sent within 1 minute
- Fresh webhooks always processed before retries
- Retry success rate > 70% for transient errors
- Comprehensive error classification and handling
- Real-time alerting system working

### Phase 4: Production Readiness (Week 4)

**Deliverables:**
- Comprehensive testing with real webhook data
- Performance testing within 30-second constraints
- Documentation and runbooks
- Integration with existing codebase
- Monitoring and operational procedures

**Tasks:**
1. Conduct load testing with realistic webhook volumes within 30s limits
2. Performance optimization for batch processing (max 3 webhooks)
3. Create operational documentation and runbooks
4. Integrate with existing webhook handlers seamlessly
5. Build alert management interfaces
6. Conduct security review and testing

**Success Criteria:**
- System processes 3 webhooks within 25 seconds consistently
- All processing completes within 30-second runtime limit
- All documentation complete and reviewed
- Seamless integration with existing webhook processors
- Alert management and monitoring fully operational

### Migration Strategy

```
Phase A: Queue Infrastructure Deployment (Week 5)
┌─────────────────┐    ┌─────────────────┐
│   Webhook       │    │   Webhook       │
│   Endpoints     │    │   Endpoints     │
│   (Current)     │    │   (Current)     │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          ▼                      ▼
┌─────────────────┐    ┌─────────────────┐
│   Direct        │    │   Queue         │
│   Processing    │    │   Processing    │
│   (100%)        │    │   (0% - Setup)  │
└─────────────────┘    └─────────────────┘

Phase B: Feature Flag Rollout (Week 6)
- Deploy queue system alongside existing handlers
- Add feature flag to route 10% of webhooks through queue
- Monitor processing times, duplicate detection, and alerts
- Gradual increase: 10% → 25% → 50% → 75%

Phase C: Full Migration (Week 7)
- Route 100% of webhooks through queue system
- Existing webhook handlers called by queue processor
- Monitor for 1 week to ensure stability
- Remove direct processing code paths

Phase D: Optimization (Week 8)
- Fine-tune batch processing and timeout handling
- Optimize duplicate detection performance
- Enhance alerting and monitoring
- Document operational procedures
```

## Configuration Management

### Environment Configuration

```typescript
interface QueueConfig {
    // Processing Configuration
    processing: {
        batchSize: number;              // Default: 3, Max: 3 (hard limit)
        timeoutMs: number;              // Default: 29000 (29 seconds)
        maxProcessingTimeMs: number;    // Default: 29000 (hard limit)
        fireAndForgetEnabled: boolean;  // Default: true
    };

    // Duplicate Detection Configuration
    duplicateDetection: {
        enabled: boolean;               // Default: true
        causalWindowSeconds: number;    // Default: 60 (API-triggered duplicates)
        timingWindowSeconds: number;    // Default: 30 (timing-based duplicates)
        bidirectionalEnabled: boolean;  // Default: true
    };

    // Retry Configuration
    retry: {
        maxRetries: number;             // Default: 3
        freshPriority: number;          // Default: 100 (fresh webhooks)
        retryPriority: number;          // Default: 50 (retry webhooks)
        retryDelaySeconds: number;      // Default: 60
    };

    // Alerting Configuration
    alerting: {
        enabled: boolean;               // Default: true
        emailEnabled: boolean;          // Default: true
        timeoutAlerts: boolean;         // Default: true
        emailRecipients: string[];      // Alert email addresses
        smtpConfig: {
            host: string;
            port: number;
            username: string;
            password: string;
        };
    };

    // Feature Flags
    features: {
        queueEnabled: boolean;          // Default: false (for gradual rollout)
        queuePercentage: number;        // Default: 0 (percentage of traffic to queue)
        timeoutDetection: boolean;      // Default: true
        batchProcessing: boolean;       // Default: true
        retryLogic: boolean;           // Default: true
    };
}
```

### Security Considerations

```typescript
interface SecurityConfig {
    // API Security
    api: {
        rateLimiting: {
            enabled: boolean;
            requestsPerMinute: number;  // Default: 100
            burstLimit: number;         // Default: 20
        };
        authentication: {
            required: boolean;          // Default: true
            apiKeyHeader: string;       // Default: 'X-API-Key'
            allowedOrigins: string[];   // CORS configuration
        };
    };

    // Data Security
    data: {
        encryptPayloads: boolean;       // Default: false (future feature)
        sanitizeLogging: boolean;       // Default: true
        auditTrail: boolean;           // Default: true
        dataRetentionDays: number;     // Default: 90
    };

    // Access Control
    access: {
        adminEndpoints: string[];       // Endpoints requiring admin access
        readOnlyEndpoints: string[];    // Endpoints for monitoring access
        ipWhitelist: string[];         // Optional IP restrictions
    };
}
```

### Performance Considerations

```typescript
interface PerformanceConfig {
    // Database Configuration
    database: {
        connectionPoolSize: number;     // Default: 10
        queryTimeoutMs: number;         // Default: 5000
        indexOptimization: boolean;     // Default: true
        batchInsertSize: number;        // Default: 100
    };

    // Caching Configuration
    caching: {
        enabled: boolean;               // Default: true
        duplicateDetectionCacheTtl: number; // Default: 300 seconds
        entityLookupCacheTtl: number;   // Default: 60 seconds
        metricsCacheTtl: number;        // Default: 30 seconds
    };

    // Resource Limits
    limits: {
        maxQueueDepth: number;          // Default: 10000
        maxPayloadSizeKb: number;       // Default: 1024
        maxProcessingTimeMs: number;    // Default: 30000
        maxRetryDelayMs: number;        // Default: 600000
    };
}
```

## Disaster Recovery

### Backup and Recovery Procedures

```yaml
backup_strategy:
  database:
    frequency: "daily"
    retention: "30 days"
    tables:
      - webhook_queue
      - webhook_queue_logs
      - duplicate_prevention_locks

  configuration:
    frequency: "on_change"
    retention: "indefinite"
    includes:
      - environment_variables
      - feature_flags
      - monitoring_rules

recovery_procedures:
  queue_corruption:
    steps:
      1. "Stop queue processing"
      2. "Restore from latest backup"
      3. "Replay missed webhooks from logs"
      4. "Resume processing"
    rto: "15 minutes"
    rpo: "1 hour"

  processing_failure:
    steps:
      1. "Identify failed webhooks"
      2. "Reset status to pending"
      3. "Increase retry limits if needed"
      4. "Monitor recovery progress"
    rto: "5 minutes"
    rpo: "0 minutes"
```

### Manual Intervention Procedures

```sql
-- Reset stuck webhooks
UPDATE webhook_queue
SET status = 'pending', retry_count = 0, started_at = NULL
WHERE status = 'processing'
AND started_at < NOW() - INTERVAL '30 minutes';

-- Manually retry failed webhook
UPDATE webhook_queue
SET status = 'pending', retry_count = 0, error_message = NULL
WHERE id = 'webhook-id-here';

-- Clear duplicate prevention locks
DELETE FROM duplicate_prevention_locks
WHERE locked_until < NOW();

-- Emergency queue drain (process all pending)
UPDATE webhook_queue
SET priority = 999
WHERE status = 'pending';
```

## Conclusion

This revised webhook queue system design provides a robust, constraint-aware solution for cross-platform duplicate prevention in the DermaCare synchronization system. The design specifically addresses Cloudflare Workers limitations while delivering comprehensive functionality including:

- **Fire-and-Forget Processing**: Immediate HTTP trigger-based processing using existing `fireAndForgetRequest` utility, eliminating dependency on cron jobs
- **Bidirectional Duplicate Prevention**: Causal relationship detection that prevents duplicates based on API-triggered vs external webhook origins
- **30-Second Runtime Compliance**: Batch processing limited to 3 webhooks with 29-second timeout detection and automatic failure handling
- **Intelligent Retry Logic**: Fresh webhook prioritization over retries, with automatic superseding of retry attempts when new data arrives
- **Database-Backed Alerting**: Comprehensive alert system with email notifications for timeout scenarios and other critical events
- **Comprehensive Queue Management**: Full-featured `WebhookQueueManager` class with status management, duplicate detection, timeout handling, and log management

**Key Advantages:**
- **Runtime Constraint Compliance**: All processing designed to complete within 30-second Cloudflare Workers limits
- **No External Dependencies**: Uses existing infrastructure and utilities, no new external services required
- **Immediate Processing**: Fire-and-forget triggers provide immediate response while maintaining reliability
- **Smart Duplicate Prevention**: Bidirectional causal relationship detection prevents false duplicates while catching real ones
- **Operational Excellence**: Built-in alerting, monitoring, and timeout handling for production reliability

The system integrates seamlessly with existing webhook processors while providing significant improvements in reliability, duplicate prevention, and operational visibility within the constraints of the Cloudflare Workers environment.
