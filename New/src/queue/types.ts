/**
 * Webhook Queue System Types
 *
 * Type definitions for the webhook queue system including queue management,
 * duplicate prevention, timeout handling, and processing results.
 *
 * @fileoverview Type definitions for webhook queue system
 * @version 1.0.0
 * @since 2025-08-06
 */

import type { APContactWebhookPayload } from "@type";
import type { CCWebhookPayload } from "@/processors/ccWebhook";

/**
 * Webhook queue status types
 */
export type WebhookQueueStatus =
	| "pending"
	| "processing"
	| "completed"
	| "failed"
	| "timeout"
	| "duplicate_skipped";

/**
 * Webhook queue log status types (final states only)
 */
export type WebhookQueueLogStatus =
	| "completed"
	| "failed"
	| "timeout"
	| "duplicate_skipped";

/**
 * Alert types for monitoring
 */
export type AlertType =
	| "webhook_timeout"
	| "high_failure_rate"
	| "queue_backlog"
	| "duplicate_storm"
	| "processing_error";

/**
 * Alert severity levels
 */
export type AlertSeverity = "low" | "medium" | "high" | "critical";

/**
 * Lock types for duplicate prevention
 */
export type LockType =
	| "patient_sync"
	| "appointment_sync"
	| "custom_field_sync";

/**
 * Platform source types
 */
export type PlatformSource = "cc" | "ap";

/**
 * Webhook payload union type
 */
export type WebhookPayload = CCWebhookPayload | APContactWebhookPayload;

/**
 * Webhook queue item for insertion
 */
export interface WebhookQueueInsert {
	source: PlatformSource;
	entityType: string;
	entityId: string;
	patientId?: string;
	appointmentId?: string;
	payload: WebhookPayload;
	priority?: number;
	maxRetries?: number;
}

/**
 * Webhook queue item from database
 */
export interface WebhookQueueItem {
	id: string;
	createdAt: Date;
	updatedAt: Date;
	status: WebhookQueueStatus;
	priority: number;
	source: PlatformSource;
	entityType: string;
	entityId: string;
	patientId?: string;
	appointmentId?: string;
	payload: WebhookPayload;
	startedAt?: Date;
	completedAt?: Date;
	retryCount: number;
	maxRetries: number;
	nextRetryAt?: Date;
	errorMessage?: string;
	processingTimeMs?: number;
	duplicateOf?: string;
	duplicateReason?: string;
}

/**
 * Webhook queue log item
 */
export interface WebhookQueueLogItem {
	id: string;
	createdAt: Date;
	updatedAt: Date;
	status: WebhookQueueLogStatus;
	priority: number;
	source: PlatformSource;
	entityType: string;
	entityId: string;
	patientId?: string;
	appointmentId?: string;
	payload: WebhookPayload;
	startedAt?: Date;
	completedAt?: Date;
	retryCount: number;
	errorMessage?: string;
	processingTimeMs?: number;
	duplicateOf?: string;
	duplicateReason?: string;
}

/**
 * Duplicate prevention lock
 */
export interface DuplicatePreventionLock {
	id: string;
	createdAt: Date;
	updatedAt: Date;
	lockType: LockType;
	patientId?: string;
	appointmentId?: string;
	source: PlatformSource;
	webhookId: string;
	expiresAt: Date;
	context?: Record<string, unknown>;
}

/**
 * Alert item
 */
export interface Alert {
	id: string;
	createdAt: Date;
	updatedAt: Date;
	alertType: AlertType;
	severity: AlertSeverity;
	title: string;
	description: string;
	webhookId?: string;
	patientId?: string;
	appointmentId?: string;
	context?: Record<string, unknown>;
	resolved: number; // 0 = false, 1 = true
	resolvedAt?: Date;
	resolvedBy?: string;
}

/**
 * Duplicate detection result
 */
export interface DuplicateDetectionResult {
	isDuplicate: boolean;
	originalWebhookId?: string;
	reason?: string;
	lockType?: LockType;
}

/**
 * Webhook processing result
 */
export interface WebhookProcessingResult {
	webhookId: string;
	status: WebhookQueueStatus;
	processingTimeMs?: number;
	errorMessage?: string;
	duplicateOf?: string;
	duplicateReason?: string;
}

/**
 * Batch processing result
 */
export interface BatchProcessingResult {
	success: boolean;
	processedCount: number;
	batchResults: WebhookProcessingResult[];
	retryCount: number;
	alertsCreated: number;
}

/**
 * Queue processing options
 */
export interface QueueProcessingOptions {
	batchSize?: number;
	maxProcessingTimeMs?: number;
	skipDuplicateCheck?: boolean;
}

/**
 * Timeout detection result
 */
export interface TimeoutDetectionResult {
	timedOutWebhooks: WebhookQueueItem[];
	alertsCreated: number;
}

/**
 * Queue statistics
 */
export interface QueueStatistics {
	pending: number;
	processing: number;
	completed: number;
	failed: number;
	timeout: number;
	duplicateSkipped: number;
	totalInQueue: number;
	oldestPendingAge?: number; // in milliseconds
}
